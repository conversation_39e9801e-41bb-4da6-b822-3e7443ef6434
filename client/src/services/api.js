import axios from 'axios';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

const plainApi = axios.create({
  baseURL: API_BASE_URL
});

export const promptsApi = {
  getAll: () => api.get('/prompts'),
  getById: (id) => api.get(`/prompts/${id}`),
  getVersion: (id, version) => api.get(`/prompts/${id}/versions/${version}`),
  update: (id, content) => api.put(`/prompts/${id}`, { content }),
};

export const evaluationsApi = {
  getAll: () => api.get('/evaluations'),
  run: (input) => api.post('/evaluations/run', { input }),
  updateAnnotation: (id, annotation) => api.put(`/evaluations/${id}/annotation`, { annotation }),
};

export const lgdEvaluationsApi = {
  getAll: () => api.get('/lgd-evaluations'),
  run: (input) => api.post('/lgd-evaluations/run', { input }),
  getStatus: (id) => api.get(`/lgd-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/lgd-evaluations/${id}/annotation`, { annotation }),
};

export const aiInterviewEvaluationsApi = {
  getAll: () => api.get('/ai-interview-evaluations'),
  run: (datasetId) => api.post('/ai-interview-evaluations/run', { datasetId }),
  getStatus: (id) => api.get(`/ai-interview-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/ai-interview-evaluations/${id}/annotation`, { annotation }),
};

export const datasetsApi = {
  getAll: () => api.get('/datasets'),
  getById: (id) => api.get(`/datasets/${id}`),
  create: (dataset) => api.post('/datasets', dataset),
  update: (id, dataset) => api.put(`/datasets/${id}`, dataset),
  delete: (id) => api.delete(`/datasets/${id}`),
};

export const dataApi = {
  getSampleTranscript: () => plainApi.get('/data/lgd_transcript.txt'),
  getSampleCompetencies: () => plainApi.get('/data/lgd_competency_guidelines.txt')
};

export default api;
